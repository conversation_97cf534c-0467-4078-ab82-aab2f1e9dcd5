//
//  ProfileView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI
import CoreData
import Foundation
import StoreKit
// 注意：暂时注释掉RevenueCat，因为当前项目可能还没有这个依赖
// import RevenueCat

/**
 * 个人中心主视图
 * 包含个人信息、订阅标签和系统设置三个主要部分
 */
struct ProfileView: View {

    // MARK: - Dependencies
    @StateObject private var authManager = AuthenticationManager.shared
    @ObservedObject private var dataManager = DataManager.shared

    // MARK: - State
    @State private var pageAppeared = false
    @State private var showSubscriptionView = false // 控制订阅页面显示
    @State private var showProductIntroduction = false // 控制产品介绍页面显示
    @State private var showRateAppAlert = false // 控制应用评分弹窗
    @State private var showFeedbackAlert = false // 控制帮助与反馈弹窗
    @State private var showAboutView = false // 控制关于页面显示

    // 降级处理相关状态
    @State private var showDowngradeBanner = false // 显示降级横幅
    @State private var isDowngraded = false // 是否已降级

    // 试用期提醒弹窗状态
    @State private var showTrialReminderModal = false // 控制试用期订阅提醒弹窗





    // 添加管理器
    @StateObject private var trialManager = TrialManager.shared
    // 暂时注释掉其他管理器，因为当前项目可能还没有这些管理器
    // @StateObject private var subscriptionManager = CloudKitSubscriptionManager.shared
    // @StateObject private var accountDeletionManager = AccountDeletionManager.shared
    // @StateObject private var revenueCatManager = RevenueCatManager.shared

    // MARK: - Computed Properties
    private var userName: String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }

    private var userID: String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }

    private var membershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        // 检查是否为试用状态
        if trialManager.isTrialActive && user.subscriptionType == "premium" {
            return "subscription.membership.premium_trial".localized
        }

        switch user.subscriptionType ?? "free" {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }

    private var expirationDate: String {
        // 优先显示试用到期时间
        if trialManager.isTrialActive, let trialExpiration = trialManager.trialExpirationDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.locale = Locale(identifier: "zh_CN")
            return formatter.string(from: trialExpiration)
        }

        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }

    var body: some View {
        let backgroundGradient = LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(hex: "#fcfff4"), location: 0.0),
                .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                .init(color: Color.white, location: 0.7),
                .init(color: Color(hex: "#fafffe"), location: 1.0)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )

        return ZStack {
            // 美化背景渐变 - 与其他页面保持一致的风格
            backgroundGradient
                .ignoresSafeArea(.all)

            // 装饰性背景元素
            VStack {
                HStack {
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 100, height: 100)
                        .offset(x: -30, y: 20)
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -10)
                }
                Spacer()
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.02))
                        .frame(width: 80, height: 80)
                        .offset(x: 20, y: 30)
                }
            }

            // 主要内容区域 - 自适应布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 个人信息组件 - 紧贴屏幕顶部和两侧，使用屏幕高度百分比
                    let userInfoAnimation = Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)

                    UserInfoSection(
                        userName: userName,
                        userID: userID,
                        membershipLevel: membershipLevel,
                        expirationDate: expirationDate
                    )
                    .frame(height: geometry.size.height * 0.25) // 使用屏幕高度的25%
                    .frame(maxWidth: .infinity) // 紧贴屏幕两侧
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 50)
                    .animation(userInfoAnimation, value: pageAppeared)

                    // 订阅标签组件 - 叠加在个人信息组件底部
                    let subscriptionAnimation = Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.3)

                    SubscriptionBannerSection {
                        handleViewPlansPressed()
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    .offset(y: -30) // 叠加效果：向上偏移30pt
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .animation(subscriptionAnimation, value: pageAppeared)

                    // 可滚动内容区域
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 降级横幅（如果需要显示）
                            if showDowngradeBanner {
                                DowngradeBannerView(
                                    onRenewTapped: {
                                        showSubscriptionView = true
                                    },
                                    onDismiss: {
                                        withAnimation(.easeOut(duration: 0.3)) {
                                            showDowngradeBanner = false
                                        }
                                    }
                                )
                                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                                .padding(.top, 10)
                                .transition(.move(edge: .top).combined(with: .opacity))
                            }

                            // 顶部空间给订阅标签预留
                            Spacer()
                                .frame(height: 0)

                            // 系统设置组件
                            SystemSettingsSection(
                                onSettingItemPressed: { settingType in
                                    handleSettingItemPressed(settingType)
                                }
                            )
                            .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                            .padding(.top, 50) // 为订阅标签留出空间
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? -50 : -10) // 向上移动50个点
                            .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: pageAppeared)

                            // 底部空白区域，确保内容不被导航栏遮挡
                            Spacer()
                                .frame(height: DesignSystem.LiquidTabBar.height + 40)
                        }
                    }
                }
            }


        }
        .onAppear {
            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
            // 加载用户数据（为后续功能预留）
            loadUserData()
        }
        // 订阅页面全屏展示
        .fullScreenCover(isPresented: $showSubscriptionView, onDismiss: {
            // 从订阅页面返回时重新加载用户数据，确保显示最新的订阅状态
            loadUserData()
        }) {
            SubscriptionView {
                // 订阅页面的返回回调
                showSubscriptionView = false
            }
        }
        .fullScreenCover(isPresented: $showProductIntroduction) {
            ProductIntroductionView()
        }
        .sheet(isPresented: $showAboutView) {
            AboutView()
        }
        .alert("rate_app.title".localized, isPresented: $showRateAppAlert) {
            Button("rate_app.rate_now".localized) {
                // 直接调用SKStoreReviewController
                requestAppStoreReview()
            }
            Button("rate_app.later".localized, role: .cancel) {
                // 稍后提醒，关闭弹窗
            }
            Button("rate_app.no_thanks".localized, role: .destructive) {
                // 不再提醒，可以在这里设置用户偏好
                setUserPreferenceForRating(false)
            }
        } message: {
            Text("rate_app.message".localized)
        }
        .alert("feedback.contact_email.title".localized, isPresented: $showFeedbackAlert) {
            Button("feedback.contact_email.confirm".localized, role: .cancel) {
                // 确定按钮，关闭弹窗
            }
        } message: {
            Text("feedback.contact_email.message".localized)
        }
        // 处理订阅降级通知
        .handleSubscriptionChanges()
        .onReceive(NotificationCenter.default.publisher(for: .subscriptionDowngraded)) { _ in
            withAnimation(.easeInOut(duration: 0.5)) {
                showDowngradeBanner = true
                isDowngraded = true
            }
        }



        // 暂时注释掉删除进度显示，因为当前项目可能还没有AccountDeletionProgressView
        /*
        .overlay(
            showDeleteAccountProgress ?
            AccountDeletionProgressView(
                deletionManager: accountDeletionManager,
                onCancel: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                },
                onComplete: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()

                    // 删除完成后跳转到登录页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        authManager.logout()
                    }
                },
                onError: { error in
                    deletionError = error
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                }
            ) : nil
        )
        */

        // 暂时注释掉试用期订阅提醒弹窗，因为当前项目可能还没有TrialSubscriptionReminderModal
        /*
        .overlay(
            showTrialReminderModal ?
            TrialSubscriptionReminderModal(isPresented: $showTrialReminderModal) {
                // 用户点击"谢谢提醒"后，关闭弹窗并跳转到订阅页面
                print("🔔 用户确认试用期提醒，跳转到订阅页面")
                showSubscriptionView = true
            } : nil
        )
        */

    }


}

// MARK: - Settings Row Component (删除这个重复的组件)
/*
private struct SettingsRow: View {
    let icon: String
    let title: String
    let titleColor: Color
    let action: () -> Void

    init(icon: String, title: String, titleColor: Color = DesignSystem.Colors.profileSettingsTextColor, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.titleColor = titleColor
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: icon)
                    .frame(width: 20, height: 20)
                    .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)

                Text(title)
                    .font(.system(
                        size: DesignSystem.Typography.Body.fontSize,
                        weight: DesignSystem.Typography.Body.fontWeight
                    ))
                    .foregroundColor(titleColor)

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
*/

// MARK: - ProfileView Extension for Action Handlers
extension ProfileView {

    // MARK: - Action Handlers

    /**
     * 处理查看会员方案按钮点击
     */
    private func handleViewPlansPressed() {
        print("查看会员方案功能")

        // 检查用户是否处于试用期内
        if trialManager.isTrialActive {
            print("🔔 用户处于试用期内，显示温馨提醒")
            // 显示试用期提醒弹窗
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showTrialReminderModal = true
            }
        } else {
            // 直接跳转到订阅页面
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showSubscriptionView = true
            }
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理设置项点击
     */
    private func handleSettingItemPressed(_ settingType: SettingType) {
        print("设置项被点击: \(settingType.displayName)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }

        switch settingType {
        case .productIntroduction:
            // 显示产品介绍页面
            showProductIntroduction = true
            print("显示产品介绍页面")
        case .rateApp:
            // 显示应用评分弹窗
            showRateAppAlert = true
            print("显示应用评分弹窗")
        case .feedback:
            // 显示帮助与反馈弹窗
            showFeedbackAlert = true
            print("显示帮助与反馈弹窗")
        case .about:
            // 显示关于页面
            showAboutView = true
            print("显示关于页面")
        case .clearAllData:
            // 显示确认对话框
            showClearDataConfirmationAlert()
            print("显示清除数据确认对话框")
        }
    }



    /**
     * 处理退出登录
     * 可作为替代方案使用，当前在alert回调中直接调用logout方法
     */
    private func handleLogout() {
        print("用户确认退出登录")

        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)

        // 添加过渡动画
        withAnimation(.easeInOut(duration: 0.3)) {
            // 暂时注释掉，因为当前项目可能还没有AuthenticationManager
            // authManager.logout()
            print("退出登录功能暂未实现")
        }
    }

    /**
     * 加载用户数据
     * 在页面出现时调用，用于加载和刷新用户信息
     */
    private func loadUserData() {
        // 此处可以添加额外的用户数据加载逻辑
        // 例如从网络或本地数据库获取最新的用户信息
        print("加载用户数据：\(userName)")

        // 刷新试用状态
        trialManager.refreshTrialStatus()

        // 强制刷新DataManager的用户数据
        dataManager.objectWillChange.send()

        // 触发订阅状态检查
        // Task {
        //     try? await Purchases.shared.customerInfo()
        // }
    }

    // MARK: - 应用评分相关方法

    /**
     * 请求App Store评分
     * 使用SKStoreReviewController显示系统评分弹窗
     */
    private func requestAppStoreReview() {
        print("🌟 请求App Store评分")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // 检查iOS版本兼容性（iOS 15.6+）
        if #available(iOS 14.0, *) {
            // 在主线程上调用SKStoreReviewController
            DispatchQueue.main.async {
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                    SKStoreReviewController.requestReview(in: windowScene)
                } else {
                    // 降级处理：如果无法获取windowScene，尝试获取其他windowScene
                    for scene in UIApplication.shared.connectedScenes {
                        if let windowScene = scene as? UIWindowScene {
                            SKStoreReviewController.requestReview(in: windowScene)
                            return
                        }
                    }
                    // 如果仍然无法获取windowScene，则不显示评分请求
                    print("⚠️ 无法获取WindowScene，跳过评分请求")
                }
            }
        } else {
            // iOS 14以下版本的降级处理
            print("⚠️ iOS版本过低，不支持应用内评分")
        }

        // 记录用户已经进行过评分请求
        setUserPreferenceForRating(true)
    }

    /**
     * 设置用户评分偏好
     * 用于记录用户是否已经评分或选择不再提醒
     */
    private func setUserPreferenceForRating(_ hasRated: Bool) {
        UserDefaults.standard.set(hasRated, forKey: "user_has_rated_app")
        UserDefaults.standard.set(Date(), forKey: "last_rating_request_date")
        print("📝 设置用户评分偏好: \(hasRated ? "已评分" : "不再提醒")")
    }

    /**
     * 检查是否应该显示评分提醒
     * 可以在适当的时机调用，比如用户完成某些操作后
     */
    private func shouldShowRatingPrompt() -> Bool {
        let hasRated = UserDefaults.standard.bool(forKey: "user_has_rated_app")
        let lastRequestDate = UserDefaults.standard.object(forKey: "last_rating_request_date") as? Date

        // 如果用户已经评分，不再提醒
        if hasRated {
            return false
        }

        // 如果最近30天内已经提醒过，不再提醒
        if let lastDate = lastRequestDate {
            let daysSinceLastRequest = Calendar.current.dateComponents([.day], from: lastDate, to: Date()).day ?? 0
            if daysSinceLastRequest < 30 {
                return false
            }
        }

        return true
    }

    /**
     * 显示清除数据确认对话框
     */
    private func showClearDataConfirmationAlert() {
        let alert = UIAlertController(
            title: "⚠️ 清除所有数据",
            message: "此操作将永久删除所有数据，包括：\n• 所有家庭成员信息\n• 积分记录和成长日记\n• 用户设置和偏好\n• 登录信息\n\n此操作无法撤销，确定要继续吗？",
            preferredStyle: .alert
        )

        // 取消按钮
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            print("用户取消了清除数据操作")
        })

        // 确认按钮 - 使用destructive样式表示危险操作
        alert.addAction(UIAlertAction(title: "确认清除", style: .destructive) { _ in
            print("用户确认清除所有数据")
            self.clearAllData()
        })

        // 显示对话框
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(alert, animated: true)
        }
    }

    /**
     * 清除所有数据
     */
    private func clearAllData() {
        print("🗑️ 开始清除所有数据")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()

        // 执行数据清除操作
        performDataClearance()
    }

    /**
     * 执行数据清除操作
     */
    private func performDataClearance() {
        print("🔄 执行数据清除操作...")

        // 1. 清除Core Data中的所有数据
        clearCoreDataEntities()

        // 2. 清除Keychain中的敏感信息
        clearKeychainData()

        // 3. 清除UserDefaults中的用户偏好设置
        clearUserDefaults()

        // 4. 清除iCloud设置同步数据
        clearUbiquitousKeyValueStore()

        // 5. 清除临时文件和缓存
        clearTemporaryFilesAndCaches()

        // 6. 重置应用状态
        resetApplicationState()

        print("✅ 所有数据清除完成")

        // 显示完成提示
        showDataClearanceCompletionAlert()
    }

    /**
     * 清除Core Data中的所有实体数据
     */
    private func clearCoreDataEntities() {
        print("🗄️ 清除Core Data数据...")

        let context = dataManager.persistenceController.container.viewContext

        // 需要清除的实体类型
        let entityNames = [
            "User",
            "Subscription",
            "GlobalRule",
            "Member",
            "PointRecord",
            "DiaryEntry",
            "AIReport",
            "MemberRule",
            "MemberPrize",
            "RedemptionRecord",
            "LotteryRecord",
            "LotteryConfig",
            "LotteryItem"
        ]

        var totalDeleted = 0

        for entityName in entityNames {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)

            do {
                let result = try context.execute(deleteRequest) as? NSBatchDeleteResult
                let deletedCount = result?.result as? Int ?? 0
                totalDeleted += deletedCount
                print("✅ 删除 \(entityName): \(deletedCount) 条记录")
            } catch {
                print("❌ 删除 \(entityName) 失败: \(error)")
            }
        }

        // 保存上下文
        do {
            try context.save()
            print("✅ Core Data清除完成，共删除 \(totalDeleted) 条记录")
        } catch {
            print("❌ 保存Core Data上下文失败: \(error)")
        }
    }

    /**
     * 清除Keychain中的敏感数据
     */
    private func clearKeychainData() {
        print("🔐 清除Keychain数据...")

        let keychainManager = KeychainManager.shared

        // 清除用户登录信息
        keychainManager.clearLoginInfo()

        // 清除API密钥
        keychainManager.deleteAPIKey()

        print("✅ Keychain数据清除完成")
    }

    /**
     * 清除UserDefaults中的用户偏好设置
     */
    private func clearUserDefaults() {
        print("📱 清除UserDefaults数据...")

        let userDefaults = UserDefaults.standard

        // 需要清除的UserDefaults键
        let keysToRemove = [
            // 用户登录相关
            "user_is_logged_in",
            "apple_user_id",
            "user_name",
            "user_email",

            // CloudKit同步相关
            "last_cloudkit_sync_date",
            "icloud_sync_enabled",
            "last_sync_date",

            // 成长日记草稿
            "GrowthDiaryDraft",

            // 应用设置
            "app_language",
            "notifications_enabled",
            "sound_enabled",
            "haptic_feedback_enabled",
            "auto_sync_enabled",
            "last_sync_timestamp",

            // 其他可能的设置
            "selected_language",
            "subscription_level",
            "first_launch",
            "tutorial_completed"
        ]

        for key in keysToRemove {
            userDefaults.removeObject(forKey: key)
        }

        // 强制同步
        userDefaults.synchronize()

        print("✅ UserDefaults数据清除完成")
    }

    /**
     * 清除NSUbiquitousKeyValueStore中的iCloud设置
     */
    private func clearUbiquitousKeyValueStore() {
        print("☁️ 清除iCloud设置数据...")

        let ubiquitousStore = NSUbiquitousKeyValueStore.default

        // 需要清除的iCloud设置键
        let iCloudKeysToRemove = [
            "app_language",
            "notifications_enabled",
            "sound_enabled",
            "haptic_feedback_enabled",
            "auto_sync_enabled",
            "last_sync_timestamp"
        ]

        for key in iCloudKeysToRemove {
            ubiquitousStore.removeObject(forKey: key)
        }

        // 强制同步到iCloud
        ubiquitousStore.synchronize()

        print("✅ iCloud设置数据清除完成")
    }

    /**
     * 清除临时文件和缓存
     */
    private func clearTemporaryFilesAndCaches() {
        print("🗂️ 清除临时文件和缓存...")

        // 清除URL缓存
        URLCache.shared.removeAllCachedResponses()

        // 清除临时目录
        clearTemporaryDirectory()

        // 清除应用缓存目录
        clearApplicationCaches()

        print("✅ 临时文件和缓存清除完成")
    }

    /**
     * 清除临时目录
     */
    private func clearTemporaryDirectory() {
        let tmpDir = NSTemporaryDirectory()
        let fileManager = FileManager.default

        do {
            let tmpFiles = try fileManager.contentsOfDirectory(atPath: tmpDir)
            for file in tmpFiles {
                let filePath = tmpDir + "/" + file
                try fileManager.removeItem(atPath: filePath)
            }
            print("✅ 临时目录清理完成")
        } catch {
            print("⚠️ 清理临时目录失败: \(error)")
        }
    }

    /**
     * 清除应用缓存目录
     */
    private func clearApplicationCaches() {
        guard let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first else {
            print("⚠️ 无法获取缓存目录")
            return
        }

        let fileManager = FileManager.default

        do {
            let cacheFiles = try fileManager.contentsOfDirectory(at: cacheDir, includingPropertiesForKeys: nil)
            for fileURL in cacheFiles {
                try fileManager.removeItem(at: fileURL)
            }
            print("✅ 应用缓存目录清理完成")
        } catch {
            print("⚠️ 清理应用缓存失败: \(error)")
        }
    }

    /**
     * 重置应用状态
     */
    private func resetApplicationState() {
        print("🔄 重置应用状态...")

        // 重置DataManager状态
        dataManager.resetToInitialState()

        // 重置设置同步管理器
        SettingsSyncManager.shared.resetToDefaults()

        // 重置iCloud同步管理器
        iCloudSyncManager.shared.resetSyncState()

        // 重置Core Data管理器
        CoreDataManager.shared.resetSyncStatus()

        print("✅ 应用状态重置完成")
    }

    /**
     * 显示数据清除完成提示
     */
    private func showDataClearanceCompletionAlert() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let alert = UIAlertController(
                title: "数据清除完成",
                message: "所有数据已成功清除。应用将重新启动以完成重置。",
                preferredStyle: .alert
            )

            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                // 退出应用，让用户重新启动
                exit(0)
            })

            // 获取当前活跃的视图控制器
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first,
               let rootViewController = window.rootViewController {
                rootViewController.present(alert, animated: true)
            }
        }
    }




}

// MARK: - Preview
#Preview {
    ProfileView()
        // 暂时注释掉，因为当前项目可能还没有AuthenticationManager
        // .environmentObject(AuthenticationManager())
        // .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}

// MARK: - Preview
#Preview {
    ProfileView()
}
